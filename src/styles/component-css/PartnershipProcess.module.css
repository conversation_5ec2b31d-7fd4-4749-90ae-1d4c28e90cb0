/* Partnership Process - Apple-style Interactive Section */

.partnershipSection {
  position: relative;
  background: linear-gradient(180deg, var(--bg-modern) 0%, var(--bg-modern-dark) 100%);
  overflow: hidden;
}

/* Fixed Container */
.fixedContainer {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.fixedContainer.fixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(0, 236, 240, 0.15) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(40px);
}

.gradientOrb2 {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(157, 78, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(30px);
}

.gridPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

/* Container */
.container {
  position: relative;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--space-6);
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Section Header */
.sectionHeader {
  text-align: center;
  margin-bottom: var(--section-heading-spacing);
}

.badge {
  display: inline-block;
  padding: var(--space-2) var(--space-4);
  background: rgba(0, 236, 240, 0.1);
  border: 1px solid rgba(0, 236, 240, 0.2);
  border-radius: var(--radius-full);
  color: var(--primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  letter-spacing: var(--tracking-wide);
  text-transform: uppercase;
  margin-bottom: var(--space-6);
}

.sectionTitle {
  font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

.gradientText {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.sectionSubtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

/* Process Timeline */
.processTimeline {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--space-16) 0;
  padding: 0 var(--space-4);
}

.timelineTrack {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
  z-index: 1;
}

.timelineProgress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 1px;
  transform-origin: left;
}

.timelineStep {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  /* transition: all var(--transition-normal); */
}

.timelineStep.active .stepNumber {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: var(--color-white);
  transform: scale(1.2);
  box-shadow: var(--glow-primary);
}

.timelineStep.active .stepLabel {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

.stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-surface);
  border: 2px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-bold);
  color: var(--text-tertiary);
  margin-bottom: var(--space-3);
  /* transition: all var(--transition-normal); */
}

.stepLabel {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  text-align: center;
  max-width: 120px;
  /* transition: all var(--transition-normal); */
}

/* Static Content Tile */
.staticContentTile {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--space-8);
}

.stepContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: center;
  padding: var(--space-8);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
}

.stepInfo {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.stepMeta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.stepBadge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
}

.stepBadge.teal {
  background: rgba(0, 236, 240, 0.1);
  color: var(--primary);
  border: 1px solid rgba(0, 236, 240, 0.2);
}

.stepBadge.purple {
  background: rgba(157, 78, 255, 0.1);
  color: var(--accent-purple);
  border: 1px solid rgba(157, 78, 255, 0.2);
}

.stepBadge.blue {
  background: rgba(61, 158, 238, 0.1);
  color: var(--secondary);
  border: 1px solid rgba(61, 158, 238, 0.2);
}

.stepBadge.green {
  background: rgba(25, 253, 178, 0.1);
  color: var(--accent-green);
  border: 1px solid rgba(25, 253, 178, 0.2);
}

.stepBadge.orange {
  background: rgba(255, 120, 70, 0.1);
  color: var(--accent-orange);
  border: 1px solid rgba(255, 120, 70, 0.2);
}

.stepSubtitle {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

.stepTitle {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
}

.stepDescription {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.stepFeatures {
  list-style: none;
  padding: 0;
  margin: var(--space-6) 0 0 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.stepFeatures li {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-base);
  color: var(--text-secondary);
  /* transition: all var(--transition-normal); */
}

.stepFeatures li i {
  color: var(--primary);
  font-size: var(--text-sm);
}

/* Device Mockups */
.stepVisual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.deviceMockup {
  position: relative;
  height: auto;
  width: 100%;
  max-height: 500px;;
  /* max-height: 100%; */
  /* transition: all var(--transition-slow); */
}

.deviceMockup.macbook {
  width: 400px;
  height: 250px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 12px 12px 0 0;
  padding: 20px 20px 0 20px;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.deviceMockup.macbook::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 420px;
  height: 8px;
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  border-radius: 0 0 20px 20px;
}

.deviceMockup.ipad {
  width: 300px;
  height: 400px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 20px;
  padding: 30px 20px;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.deviceMockup.imac {
  width: 350px;
  height: 220px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 8px;
  padding: 15px;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.deviceMockup.imac::after {
  content: '';
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 40px;
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  border-radius: 0 0 20px 20px;
}

.deviceMockup.iphone {
  width: 200px;
  height: 400px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 30px;
  padding: 30px 15px;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.deviceMockup.watch {
  width: 180px;
  height: 220px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 40px;
  padding: 25px 20px;
  box-shadow:
    0 0 0 2px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.deviceScreen {
  width: 100%;
  height: 100%;
  background: var(--bg-dark);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.screenContent {
  width: 100%;
  height: 100%;
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.screenContent.teal {
  background: linear-gradient(135deg, rgba(0, 236, 240, 0.1), rgba(0, 236, 240, 0.05));
}

.screenContent.purple {
  background: linear-gradient(135deg, rgba(157, 78, 255, 0.1), rgba(157, 78, 255, 0.05));
}

.screenContent.blue {
  background: linear-gradient(135deg, rgba(61, 158, 238, 0.1), rgba(61, 158, 238, 0.05));
}

.screenContent.green {
  background: linear-gradient(135deg, rgba(25, 253, 178, 0.1), rgba(25, 253, 178, 0.05));
}

.screenContent.orange {
  background: linear-gradient(135deg, rgba(255, 120, 70, 0.1), rgba(255, 120, 70, 0.05));
}

.contentPlaceholder {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  height: 100%;
}

.placeholderHeader {
  height: 20%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
}

.placeholderBody {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
}

.placeholderFooter {
  height: 15%;
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-md);
}

/* Call to Action */
.ctaSection {
  text-align: center;
  margin-top: var(--space-20);
  padding: var(--space-12);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.ctaDescription {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  line-height: var(--leading-relaxed);
}

.ctaActions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  align-items: center;
}

.primaryCta {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: var(--color-white);
  text-decoration: none;
  border-radius: var(--radius-full);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  /* transition: all var(--transition-normal); */
  box-shadow: var(--shadow-primary);
}

.primaryCta:hover {
  box-shadow: var(--glow-primary);
  transform: translateY(-2px);
}

.secondaryCta {
  display: inline-flex;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: transparent;
  color: var(--text-primary);
  text-decoration: none;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-full);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  /* transition: all var(--transition-normal); */
}

.secondaryCta:hover {
  background: var(--bg-surface-hover);
  border-color: var(--border-focus);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--space-4);
  }

  .sectionTitle {
    font-size: var(--text-4xl);
  }

  .stepContent {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    text-align: center;
  }

  .deviceMockup.macbook {
    width: 320px;
    height: 200px;
  }

  .deviceMockup.ipad {
    width: 240px;
    height: 320px;
  }

  .deviceMockup.imac {
    width: 280px;
    height: 180px;
  }

  .deviceMockup.iphone {
    width: 160px;
    height: 320px;
  }

  .deviceMockup.watch {
    width: 140px;
    height: 180px;
  }

  .staticContentTile {
    min-height: 500px;
  }

  .timelineStep {
    flex-direction: column;
  }

  .stepLabel {
    font-size: var(--text-xs);
    max-width: 80px;
  }
}

@media (max-width: 768px) {
  .partnershipSection {
    padding: var(--section-spacing-y-mobile) 0;
  }

  .container {
    padding: 0 var(--space-3);
  }

  .sectionTitle {
    font-size: var(--text-3xl);
  }

  .sectionSubtitle {
    font-size: var(--text-lg);
  }

  .processTimeline {
    flex-direction: column;
    gap: var(--space-4);
    margin: var(--space-12) 0;
  }

  .timelineTrack {
    display: none;
  }

  .timelineStep {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: var(--space-3);
    width: 100%;
  }

  .stepNumber {
    width: 32px;
    height: 32px;
    margin-bottom: 0;
  }

  .stepLabel {
    text-align: left;
    max-width: none;
    font-size: var(--text-sm);
  }

  .stepContent {
    padding: var(--space-6);
    gap: var(--space-6);
  }

  .stepTitle {
    font-size: var(--text-2xl);
  }

  .stepDescription {
    font-size: var(--text-base);
  }

  .deviceMockup.macbook {
    width: 280px;
    height: 175px;
    padding: 15px 15px 0 15px;
  }

  .deviceMockup.ipad {
    width: 200px;
    height: 280px;
    padding: 25px 15px;
  }

  .deviceMockup.imac {
    width: 240px;
    height: 150px;
    padding: 12px;
  }

  .deviceMockup.iphone {
    width: 140px;
    height: 280px;
    padding: 25px 12px;
  }

  .deviceMockup.watch {
    width: 120px;
    height: 150px;
    padding: 20px 15px;
  }

  .ctaActions {
    flex-direction: column;
    gap: var(--space-3);
  }

  .primaryCta,
  .secondaryCta {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .sectionTitle {
    font-size: var(--text-2xl);
  }

  .badge {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-3);
  }

  .stepContent {
    padding: var(--space-4);
  }

  .stepTitle {
    font-size: var(--text-xl);
  }

  .ctaTitle {
    font-size: var(--text-2xl);
  }

  .ctaDescription {
    font-size: var(--text-base);
  }

  .deviceMockup.macbook {
    width: 240px;
    height: 150px;
    padding: 12px 12px 0 12px;
  }

  .deviceMockup.ipad {
    width: 160px;
    height: 240px;
    padding: 20px 12px;
  }

  .deviceMockup.imac {
    width: 200px;
    height: 125px;
    padding: 10px;
  }

  .deviceMockup.iphone {
    width: 120px;
    height: 240px;
    padding: 20px 10px;
  }

  .deviceMockup.watch {
    width: 100px;
    height: 125px;
    padding: 15px 12px;
  }
}

/* Animation Enhancements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.deviceMockup {
  animation: float 6s ease-in-out infinite;
}

.deviceMockup.macbook {
  animation-delay: 0s;
}

.deviceMockup.ipad {
  animation-delay: 1s;
}

.deviceMockup.imac {
  animation-delay: 2s;
}

.deviceMockup.iphone {
  animation-delay: 3s;
}

.deviceMockup.watch {
  animation-delay: 4s;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .deviceMockup {
    animation: none;
  }

  .gradientOrb1,
  .gradientOrb2 {
    transform: none !important;
  }

  .timelineProgress {
    transition: none;
  }


}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stepContent {
    border: 2px solid var(--text-primary);
  }

  .deviceMockup {
    border: 1px solid var(--text-primary);
  }

  .badge {
    border: 1px solid currentColor;
  }
}
