'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import styles from '@/styles/component-css/PartnershipProcess.module.css'

export default function PartnershipProcess() {
  const [activeStep, setActiveStep] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [isFixed, setIsFixed] = useState(false)
  const sectionRef = useRef(null)
  const contentRef = useRef(null)

  // Create a tall section to capture scroll events
  const sectionHeight = 4000 // Height to allow for scroll-based transitions

  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end end"]
  })

  // Transform scroll progress to different animation values
  const y = useTransform(scrollYProgress, [0, 1], [100, -100])
  const opacity = useTransform(scrollYProgress, [0, 0.1, 0.9, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.1, 0.9, 1], [0.8, 1, 1, 0.8])

const processSteps = [
  {
    id: 'discovery',
    title: 'Discovery & Strategy',
    subtitle: 'Getting to know your goals',
    description: 'We begin by having in-depth conversations about your vision, your audience, and the technical needs of your project. Our team also carries out thorough research to understand your market and the competition.',
    features: [
      'Chatting with key stakeholders',
      'Looking into your market and competitors',
      'Listing out the technical requirements',
      'Planning the project roadmap'
    ],
    deviceMockup: 'macbook',
    color: 'teal',
    duration: '1–2 weeks'
  },
  {
    id: 'design',
    title: 'Design & Prototyping',
    subtitle: 'Turning ideas into visuals',
    description: 'Next, our designers sketch out wireframes and build interactive prototypes. We’ll create polished visuals that reflect your brand and make sure everything works beautifully across devices.',
    features: [
      'Crafting the user experience',
      'Shaping your brand’s visual identity',
      'Interactive design mockups',
      'Design systems that scale'
    ],
    deviceMockup: 'ipad',
    color: 'purple',
    duration: '2–3 weeks'
  },
  {
    id: 'development',
    title: 'Development & Testing',
    subtitle: 'Building it all properly',
    description: 'Using modern tech and best practices, we bring your project to life with clean, reliable code. We also test thoroughly to make sure everything runs smoothly before launch.',
    features: [
      'Built with a modern tech stack',
      'Fully responsive for all devices',
      'Optimised for performance',
      'Tested inside and out'
    ],
    deviceMockup: 'imac',
    color: 'blue',
    duration: '4–8 weeks'
  },
  {
    id: 'launch',
    title: 'Launch & Optimisation',
    subtitle: 'Going live with confidence',
    description: 'We manage the entire launch process for you – from setting up hosting to keeping an eye on performance. After launch, we make sure everything keeps running at its best.',
    features: [
      'Setup for hosting and deployment',
      'Ongoing performance tracking',
      'SEO tuned and ready',
      'Analytics in place from day one'
    ],
    deviceMockup: 'iphone',
    color: 'green',
    duration: '1 week'
  },
  {
    id: 'support',
    title: 'Ongoing Support',
    subtitle: 'Here for the long run',
    description: 'We don’t just disappear after launch. We’re here to provide updates, improvements, and help with anything technical so your project continues to thrive.',
    features: [
      'Regular updates and care',
      'Adding new features over time',
      'Friendly technical support',
      'Keeping an eye on performance'
    ],
    deviceMockup: 'watch',
    color: 'orange',
    duration: 'Ongoing'
  }
]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [isVisible])

  // Handle fixed positioning and step progression based on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return

      const sectionRect = sectionRef.current.getBoundingClientRect()
      const sectionTop = sectionRect.top
      const sectionBottom = sectionRect.bottom
      const viewportHeight = window.innerHeight

      // Calculate scroll progress within the section
      const scrolledDistance = Math.abs(sectionTop)
      const totalScrollDistance = sectionRect.height - viewportHeight
      const progress = Math.min(scrolledDistance / totalScrollDistance, 1)

      // Update active step based on progress
      const totalSteps = processSteps.length

      // Determine if should be fixed based on progress and position
      // Fixed when: section has started scrolling AND we're still within the interactive zone
      const hasStartedScrolling = sectionTop <= 0
      const interactiveZoneEnd = 0.9 // 90% through the section
      const isInInteractiveZone = progress < interactiveZoneEnd
      const shouldBeFixed = hasStartedScrolling && isInInteractiveZone && sectionBottom > viewportHeight

      setIsFixed(shouldBeFixed)

      // Update active step based on progress within the interactive zone
      if (isInInteractiveZone) {
        // Map progress to steps within the interactive zone (0-90% of section)
        const interactiveProgress = progress / interactiveZoneEnd
        const stepIndex = Math.floor(interactiveProgress * totalSteps)
        const clampedStepIndex = Math.min(stepIndex, totalSteps - 1)

        if (clampedStepIndex !== activeStep) {
          setActiveStep(clampedStepIndex)
        }
      } else {
        // Ensure we're showing the last step when in the exit zone
        if (activeStep !== totalSteps - 1) {
          setActiveStep(totalSteps - 1)
        }
      }
    }

    // Initial check
    handleScroll()

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [activeStep, processSteps.length])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  }

  return (
    <section
      className={styles.partnershipSection}
      id="partnership-process"
      ref={sectionRef}
      style={{ height: `${sectionHeight}px` }}
    >
      {/* Fixed Content Container */}
      <div
        className={`${styles.fixedContainer} ${isFixed ? styles.fixed : ''}`}
        ref={contentRef}
      >
        {/* Background Elements */}
        <div className={styles.backgroundElements}>
          <motion.div
            className={styles.gradientOrb1}
            style={{ y, opacity }}
          />
          <motion.div
            className={styles.gradientOrb2}
            style={{ y: useTransform(y, (value) => value * -0.5) }}
          />
          <div className={styles.gridPattern} />
        </div>

        <motion.div
          className={styles.container}
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
        >
        {/* Section Header */}
        <motion.div className={styles.sectionHeader} variants={itemVariants}>
          <div className={styles.badge}>Partnership Process</div>
          <h2 className={styles.sectionTitle}>
            Experience the
            <span className={styles.gradientText}> Flat 18 Difference</span>
          </h2>
          <p className={styles.sectionSubtitle}>
            From initial concept to ongoing success, we guide you through every step
            of your digital transformation journey with precision and care.
          </p>
        </motion.div>

        {/* Process Timeline */}
        <motion.div className={styles.processTimeline} variants={itemVariants}>
          <div className={styles.timelineTrack}>
            <motion.div
              className={styles.timelineProgress}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: isVisible ? (activeStep + 1) / processSteps.length : 0 }}
              transition={{ duration: 1, ease: "easeInOut" }}
            />
          </div>

          {processSteps.map((step, index) => (
            <motion.div
              key={step.id}
              className={`${styles.timelineStep} ${index <= activeStep ? styles.active : ''}`}
              variants={itemVariants}
              transition={{ delay: index * 0.1 }}
            >
              <div className={styles.stepNumber}>{index + 1}</div>
              <div className={styles.stepLabel}>{step.title}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Single Static Content Tile */}
        <motion.div className={styles.staticContentTile} variants={itemVariants}>
          <AnimatePresence mode="wait">
            <motion.div
              key={activeStep}
              className={styles.stepContent}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{
                duration: 0.6,
                ease: [0.22, 1, 0.36, 1]
              }}
            >
              <div className={styles.stepInfo}>
                <motion.div
                  className={styles.stepMeta}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
                >
                  <span className={`${styles.stepBadge} ${styles[processSteps[activeStep]?.color]}`}>
                    {processSteps[activeStep]?.duration}
                  </span>
                  <span className={styles.stepSubtitle}>{processSteps[activeStep]?.subtitle}</span>
                </motion.div>

                <motion.h3
                  className={styles.stepTitle}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
                >
                  {processSteps[activeStep]?.title}
                </motion.h3>

                <motion.p
                  className={styles.stepDescription}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
                >
                  {processSteps[activeStep]?.description}
                </motion.p>

                <motion.ul
                  className={styles.stepFeatures}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.7, ease: "easeOut", delay: 0.4 }}
                >
                  {processSteps[activeStep]?.features.map((feature, featureIndex) => (
                    <motion.li
                      key={`${activeStep}-${featureIndex}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        delay: 0.5 + (featureIndex * 0.1),
                        duration: 0.5,
                        ease: "easeOut"
                      }}
                    >
                      <i className="bi bi-check-circle-fill" />
                      {feature}
                    </motion.li>
                  ))}
                </motion.ul>
              </div>

              <div className={styles.stepVisual}>
                <motion.div
                  className={`${styles.deviceMockup} ${styles[processSteps[activeStep]?.deviceMockup]}`}
                  initial={{ opacity: 0, scale: 0.8, rotateY: 15 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{
                    duration: 0.8,
                    ease: [0.22, 1, 0.36, 1],
                    delay: 0.3
                  }}
                >
                  <div className={styles.deviceScreen}>
                    <motion.div
                      className={`${styles.screenContent} ${styles[processSteps[activeStep]?.color]}`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                    >
                      <div className={styles.contentPlaceholder}>
                        <motion.div
                          className={styles.placeholderHeader}
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.8, delay: 0.7 }}
                        />
                        <motion.div
                          className={styles.placeholderBody}
                          initial={{ height: 0 }}
                          animate={{ height: "100%" }}
                          transition={{ duration: 0.8, delay: 0.8 }}
                        />
                        <motion.div
                          className={styles.placeholderFooter}
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 0.8, delay: 0.9 }}
                        />
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </AnimatePresence>
        </motion.div>

        {/* Call to Action */}
        <motion.div className={styles.ctaSection} variants={itemVariants}>
          <div className={styles.ctaContent}>
            <h3 className={styles.ctaTitle}>Ready to start your journey?</h3>
            <p className={styles.ctaDescription}>
              Let's discuss your project and create something extraordinary together.
            </p>
            <div className={styles.ctaActions}>
              <motion.a
                href="#chat"
                className={styles.primaryCta}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Start Your Project</span>
                <i className="bi bi-arrow-right" />
              </motion.a>
              <motion.a
                href="#work"
                className={styles.secondaryCta}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Our Work
              </motion.a>
            </div>
          </div>
        </motion.div>
      </motion.div>
      </div>
    </section>
  )
}
