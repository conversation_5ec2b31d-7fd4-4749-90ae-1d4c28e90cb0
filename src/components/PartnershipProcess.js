'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, useScroll, useTransform } from 'framer-motion'
import styles from '@/styles/component-css/PartnershipProcess.module.css'
import Image from 'next/image'

export default function PartnershipProcess() {
  const [activeStep, setActiveStep] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef(null)
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  })

  // Transform scroll progress to different animation values
  const y = useTransform(scrollYProgress, [0, 1], [100, -100])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8])

const processSteps = [
  {
    id: 'discovery',
    title: 'Discovery & Strategy',
    subtitle: 'Getting to know your goals',
    description: 'We begin by having in-depth conversations about your vision, your audience, and the technical needs of your project. Our team also carries out thorough research to understand your market and the competition.',
    features: [
      'Chatting with key stakeholders',
      'Looking into your market and competitors',
      'Listing out the technical requirements',
      'Planning the project roadmap'
    ],
    deviceMockup: 'images/deviceMockup/macbook.png',
    color: 'teal',
    duration: '1–2 weeks'
  },
  {
    id: 'design',
    title: 'Design & Prototyping',
    subtitle: 'Turning ideas into visuals',
    description: 'Next, our designers sketch out wireframes and build interactive prototypes. We’ll create polished visuals that reflect your brand and make sure everything works beautifully across devices.',
    features: [
      'Crafting the user experience',
      'Shaping your brand’s visual identity',
      'Interactive design mockups',
      'Design systems that scale'
    ],
    deviceMockup: 'images/deviceMockup/ipad.png',
    color: 'purple',
    duration: '2–3 weeks'
  },
  {
    id: 'development',
    title: 'Development & Testing',
    subtitle: 'Building it all properly',
    description: 'Using modern tech and best practices, we bring your project to life with clean, reliable code. We also test thoroughly to make sure everything runs smoothly before launch.',
    features: [
      'Built with a modern tech stack',
      'Fully responsive for all devices',
      'Optimised for performance',
      'Tested inside and out'
    ],
    deviceMockup: 'images/deviceMockup/imac.png',
    color: 'blue',
    duration: '4–8 weeks'
  },
  {
    id: 'launch',
    title: 'Launch & Optimisation',
    subtitle: 'Going live with confidence',
    description: 'We manage the entire launch process for you – from setting up hosting to keeping an eye on performance. After launch, we make sure everything keeps running at its best.',
    features: [
      'Setup for hosting and deployment',
      'Ongoing performance tracking',
      'SEO tuned and ready',
      'Analytics in place from day one'
    ],
    deviceMockup: 'images/deviceMockup/iphone.png',
    color: 'green',
    duration: '1 week'
  },
  {
    id: 'support',
    title: 'Ongoing Support',
    subtitle: 'Here for the long run',
    description: 'We don’t just disappear after launch. We’re here to provide updates, improvements, and help with anything technical so your project continues to thrive.',
    features: [
      'Regular updates and care',
      'Adding new features over time',
      'Friendly technical support',
      'Keeping an eye on performance'
    ],
    deviceMockup: 'images/deviceMockup/watch.png',
    color: 'orange',
    duration: 'Ongoing'
  }
]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [isVisible])

  // Auto-advance steps based on scroll position
  useEffect(() => {
    const unsubscribe = scrollYProgress.onChange((latest) => {
      const stepIndex = Math.floor(latest * processSteps.length)
      setActiveStep(Math.min(stepIndex, processSteps.length - 1))
    })

    return unsubscribe
  }, [scrollYProgress, processSteps.length])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  }

  return (
    <section className={styles.partnershipSection} id="partnership-process" ref={sectionRef}>
      {/* Background Elements */}
      <div className={styles.backgroundElements}>
        <motion.div 
          className={styles.gradientOrb1}
          style={{ y, opacity }}
        />
        <motion.div 
          className={styles.gradientOrb2}
          style={{ y: useTransform(y, (value) => value * -0.5) }}
        />
        <div className={styles.gridPattern} />
      </div>

      <motion.div 
        className={styles.container}
        style={{ scale, opacity }}
        variants={containerVariants}
        initial="hidden"
        animate={isVisible ? "visible" : "hidden"}
      >
        {/* Section Header */}
        <motion.div className={styles.sectionHeader} variants={itemVariants}>
          <div className={styles.badge}>Partnership Process</div>
          <h2 className={styles.sectionTitle}>
            Experience the
            <span className={styles.gradientText}> Flat 18 Difference</span>
          </h2>
          <p className={styles.sectionSubtitle}>
            From initial concept to ongoing success, we guide you through every step 
            of your digital transformation journey with precision and care.
          </p>
        </motion.div>

        {/* Process Timeline */}
        <motion.div className={styles.processTimeline} variants={itemVariants}>
          <div className={styles.timelineTrack}>
            <motion.div 
              className={styles.timelineProgress}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: isVisible ? (activeStep + 1) / processSteps.length : 0 }}
              transition={{ duration: 1, ease: "easeInOut" }}
            />
          </div>
          
          {processSteps.map((step, index) => (
            <motion.div
              key={step.id}
              className={`${styles.timelineStep} ${index <= activeStep ? styles.active : ''}`}
              variants={itemVariants}
              transition={{ delay: index * 0.1 }}
            >
              <div className={styles.stepNumber}>{index + 1}</div>
              <div className={styles.stepLabel}>{step.title}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Process Steps */}
        <div className={styles.processSteps}>
          {processSteps.map((step, index) => (
            <motion.div
              key={step.id}
              className={`${styles.processStep} ${index === activeStep ? styles.active : ''}`}
              variants={itemVariants}
              transition={{ delay: index * 0.2 }}
            >
              <div className={styles.stepContent}>
                <div className={styles.stepInfo}>
                  <div className={styles.stepMeta}>
                    <span className={`${styles.stepBadge} ${styles[step.color]}`}>
                      {step.duration}
                    </span>
                    <span className={styles.stepSubtitle}>{step.subtitle}</span>
                  </div>
                  
                  <h3 className={styles.stepTitle}>{step.title}</h3>
                  <p className={styles.stepDescription}>{step.description}</p>
                  
                  <ul className={styles.stepFeatures}>
                    {step.features.map((feature, featureIndex) => (
                      <motion.li
                        key={featureIndex}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ 
                          opacity: index === activeStep ? 1 : 0.5, 
                          x: 0 
                        }}
                        transition={{ 
                          delay: featureIndex * 0.1,
                          duration: 0.5 
                        }}
                      >
                        <i className="bi bi-check-circle-fill" />
                        {feature}
                      </motion.li>
                    ))}
                  </ul>
                </div>

                <div className={styles.stepVisual}>
                  <motion.div
                    
                    animate={{
                      scale: index === activeStep ? 1 : 0.9,
                      opacity: index === activeStep ? 1 : 0.6
                    }}
                    transition={{ duration: 0.6, ease: "easeInOut" }}
                  >
                    <Image src={`${step.deviceMockup}`} className={styles.deviceMockup} alt={`${step.title} mockup`} width={500} height={500} />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div className={styles.ctaSection} variants={itemVariants}>
          <div className={styles.ctaContent}>
            <h3 className={styles.ctaTitle}>Ready to start your journey?</h3>
            <p className={styles.ctaDescription}>
              Let's discuss your project and create something extraordinary together.
            </p>
            <div className={styles.ctaActions}>
              <motion.a
                href="#chat"
                className={styles.primaryCta}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Start Your Project</span>
                <i className="bi bi-arrow-right" />
              </motion.a>
              <motion.a
                href="#work"
                className={styles.secondaryCta}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Our Work
              </motion.a>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  )
}
